<script setup lang="ts">
import type { mastodon } from '#shared/types'

const emit = defineEmits<{
  uploaded: [status: mastodon.v1.Status]
  close: []
}>()

const { loggedIn } = useAuth()

// Form state
const title = ref('')
const description = ref('')
const selectedFile = ref<File | null>(null)
const dragOver = ref(false)
const uploading = ref(false)
const uploadProgress = ref(0)
const error = ref('')

// File validation
const ALLOWED_TYPES = [
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/vnd.ms-powerpoint',
  'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  'text/plain',
  'text/csv',
  'application/rtf',
  'application/vnd.oasis.opendocument.text',
  'application/vnd.oasis.opendocument.spreadsheet',
  'application/vnd.oasis.opendocument.presentation',
  'application/zip',
  'application/x-zip-compressed',
  'application/epub+zip',
]

const MAX_FILE_SIZE = 50 * 1024 * 1024 // 50MB

const fileInput = ref<HTMLInputElement>()

const isFormValid = computed(() => {
  return title.value.trim() && selectedFile.value && !uploading.value
})

const fileTypeDisplay = computed(() => {
  if (!selectedFile.value) return ''
  const type = selectedFile.value.type
  const typeMap: Record<string, string> = {
    'application/pdf': 'PDF',
    'application/msword': 'Word Document',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'Word Document',
    'application/vnd.ms-excel': 'Excel Spreadsheet',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'Excel Spreadsheet',
    'application/vnd.ms-powerpoint': 'PowerPoint Presentation',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'PowerPoint Presentation',
    'text/plain': 'Text File',
    'text/csv': 'CSV File',
    'application/rtf': 'Rich Text Format',
    'application/zip': 'ZIP Archive',
    'application/x-zip-compressed': 'ZIP Archive',
    'application/epub+zip': 'EPUB Book',
  }
  return typeMap[type] || type
})

const fileSizeDisplay = computed(() => {
  if (!selectedFile.value) return ''
  const size = selectedFile.value.size
  if (size < 1024) return `${size} B`
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`
  return `${(size / (1024 * 1024)).toFixed(1)} MB`
})

function handleFileSelect(event: Event) {
  const target = event.target as HTMLInputElement
  if (target.files && target.files[0]) {
    selectFile(target.files[0])
  }
}

function handleDrop(event: DragEvent) {
  event.preventDefault()
  dragOver.value = false

  if (event.dataTransfer?.files && event.dataTransfer.files[0]) {
    selectFile(event.dataTransfer.files[0])
  }
}

function handleDragOver(event: DragEvent) {
  event.preventDefault()
  dragOver.value = true
}

function handleDragLeave() {
  dragOver.value = false
}

function selectFile(file: File) {
  error.value = ''

  // Validate file type
  if (!ALLOWED_TYPES.includes(file.type)) {
    error.value = `File type "${file.type}" is not allowed. Please select a supported document format.`
    return
  }

  // Validate file size
  if (file.size > MAX_FILE_SIZE) {
    error.value = `File size (${fileSizeDisplay.value}) exceeds the maximum limit of 50MB.`
    return
  }

  selectedFile.value = file

  // Auto-fill title from filename if empty
  if (!title.value.trim()) {
    const filename = file.name
    const nameWithoutExt = filename.substring(0, filename.lastIndexOf('.')) || filename
    title.value = nameWithoutExt
  }
}

function removeFile() {
  selectedFile.value = null
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

function openFileDialog() {
  fileInput.value?.click()
}

async function uploadDocument() {
  if (!isFormValid.value) return

  uploading.value = true
  uploadProgress.value = 0
  error.value = ''

  try {
    const formData = new FormData()
    formData.append('title', title.value.trim())
    formData.append('description', description.value.trim())
    formData.append('file', selectedFile.value!)

    const response = await $fetch<mastodon.v1.Status>('/api/statuses/upload-document', {
      method: 'POST',
      body: formData,
      onUploadProgress: (progress) => {
        uploadProgress.value = Math.round((progress.loaded / progress.total) * 100)
      },
    })

    emit('uploaded', response)
    resetForm()

  } catch (err: any) {
    console.error('Upload failed:', err)
    error.value = err.data?.message || 'Failed to upload document. Please try again.'
  } finally {
    uploading.value = false
    uploadProgress.value = 0
  }
}

function resetForm() {
  title.value = ''
  description.value = ''
  selectedFile.value = null
  error.value = ''
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}
</script>

<template>
  <div v-if="loggedIn" class="document-upload">
    <div class="upload-header">
      <h2>Upload Document</h2>
      <button class="close-btn" @click="emit('close')">×</button>
    </div>

    <form @submit.prevent="uploadDocument">
      <!-- File Drop Zone -->
      <div class="file-drop-zone" :class="{ 'drag-over': dragOver, 'has-file': selectedFile }" @drop="handleDrop"
        @dragover="handleDragOver" @dragleave="handleDragLeave" @click="openFileDialog">
        <input ref="fileInput" type="file" :accept="ALLOWED_TYPES.join(',')" style="display: none"
          @change="handleFileSelect">

        <div v-if="!selectedFile" class="drop-zone-content">
          <div class="upload-icon">📄</div>
          <p class="drop-text">
            <strong>Click to select</strong> or drag and drop a document
          </p>
          <p class="file-types">
            Supported: PDF, Word, Excel, PowerPoint, Text, RTF, ODT, ZIP, EPUB
          </p>
          <p class="file-size-limit">Maximum size: 50MB</p>
        </div>

        <div v-else class="selected-file">
          <div class="file-info">
            <div class="file-icon">📄</div>
            <div class="file-details">
              <div class="file-name">{{ selectedFile.name }}</div>
              <div class="file-meta">{{ fileTypeDisplay }} • {{ fileSizeDisplay }}</div>
            </div>
            <button type="button" class="remove-file" @click.stop="removeFile">×</button>
          </div>
        </div>
      </div>

      <!-- Form Fields -->
      <div class="form-fields">
        <InputBox v-model="title" label="Title" placeholder="Enter document title" required :disabled="uploading" />

        <label class="textarea-field">
          <div class="label">Description (optional)</div>
          <textarea v-model="description" placeholder="Enter document description" rows="3" :disabled="uploading" />
        </label>
      </div>

      <!-- Upload Progress -->
      <div v-if="uploading" class="upload-progress">
        <div class="progress-bar">
          <div class="progress-fill" :style="{ width: `${uploadProgress}%` }"></div>
        </div>
        <p class="progress-text">Uploading... {{ uploadProgress }}%</p>
      </div>

      <!-- Error Message -->
      <div v-if="error" class="error-message">
        {{ error }}
      </div>

      <!-- Actions -->
      <div class="upload-actions">
        <button type="button" class="btn-secondary" @click="emit('close')" :disabled="uploading">
          Cancel
        </button>
        <button type="submit" class="btn-primary" :disabled="!isFormValid">
          <span v-if="uploading">Uploading...</span>
          <span v-else>Upload Document</span>
        </button>
      </div>
    </form>
  </div>
</template>

<style lang="scss" scoped>
.document-upload {
  max-width: 600px;
  margin: 0 auto;
  padding: var(--padding-big);
}

.upload-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--padding-big);

  h2 {
    margin: 0;
    font-size: var(--font-size-h2);
  }

  .close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    padding: var(--padding-mini);
    border-radius: var(--border-radius-base);

    &:hover {
      background-color: var(--color-hover);
    }
  }
}

.file-drop-zone {
  border: 2px dashed var(--color-border);
  border-radius: var(--border-radius-base);
  padding: var(--padding-big);
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: var(--padding-big);

  &.drag-over {
    border-color: var(--color-accent);
    background-color: var(--color-accent-bg);
  }

  &.has-file {
    border-style: solid;
    border-color: var(--color-success);
  }

  &:hover:not(.has-file) {
    border-color: var(--color-accent);
  }
}

.drop-zone-content {
  .upload-icon {
    font-size: 3rem;
    margin-bottom: var(--padding-base);
  }

  .drop-text {
    font-size: var(--font-size-body);
    margin-bottom: var(--padding-mini);
  }

  .file-types,
  .file-size-limit {
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    margin: var(--padding-mini) 0;
  }
}

.selected-file {
  .file-info {
    display: flex;
    align-items: center;
    gap: var(--padding-base);
    text-align: left;
  }

  .file-icon {
    font-size: 2rem;
  }

  .file-details {
    flex: 1;
  }

  .file-name {
    font-weight: 500;
    margin-bottom: var(--padding-mini);
  }

  .file-meta {
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
  }

  .remove-file {
    background: var(--color-error);
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    cursor: pointer;
    font-size: 1rem;

    &:hover {
      background: var(--color-error-dark);
    }
  }
}

.form-fields {
  margin-bottom: var(--padding-big);

  .textarea-field {
    display: block;
    margin-top: var(--padding-base);

    .label {
      color: var(--color-label);
      margin-bottom: var(--padding-mini);
      font-size: var(--font-size-sm);
    }

    textarea {
      width: 100%;
      padding: var(--padding-base);
      border: 1px solid var(--color-border);
      border-radius: var(--border-radius-base);
      background-color: var(--color-input-bg);
      font-family: inherit;
      font-size: var(--font-size-body);
      resize: vertical;

      &:focus {
        outline: none;
        box-shadow: 0 0 0 3px var(--color-focus);
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }
  }
}

.upload-progress {
  margin-bottom: var(--padding-base);

  .progress-bar {
    width: 100%;
    height: 8px;
    background-color: var(--color-bg-secondary);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: var(--padding-mini);
  }

  .progress-fill {
    height: 100%;
    background-color: var(--color-accent);
    transition: width 0.3s ease;
  }

  .progress-text {
    text-align: center;
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
  }
}

.error-message {
  background-color: var(--color-error-bg);
  color: var(--color-error);
  padding: var(--padding-base);
  border-radius: var(--border-radius-base);
  margin-bottom: var(--padding-base);
  font-size: var(--font-size-sm);
}

.upload-actions {
  display: flex;
  gap: var(--padding-base);
  justify-content: flex-end;

  .btn-secondary,
  .btn-primary {
    padding: var(--padding-base) var(--padding-big);
    border-radius: var(--border-radius-base);
    font-size: var(--font-size-body);
    cursor: pointer;
    transition: all 0.2s ease;

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }

  .btn-secondary {
    background: var(--color-bg-secondary);
    border: 1px solid var(--color-border);
    color: var(--color-text);

    &:hover:not(:disabled) {
      background: var(--color-hover);
    }
  }

  .btn-primary {
    background: var(--color-accent);
    border: 1px solid var(--color-accent);
    color: white;

    &:hover:not(:disabled) {
      background: var(--color-accent-dark);
    }
  }
}
</style>

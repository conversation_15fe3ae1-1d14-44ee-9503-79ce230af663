import { createObject } from '.'
import type { Actor } from '../actors'

export const DOCUMENT = 'Document'

export interface DocumentProperties {
  title: string
  description?: string
  filename: string
  original_filename: string
  file_type: string
  file_size: number
  file_hash?: string
  published?: boolean
  download_count?: number
  published_date?: string
}

export async function createDocument(
  domain: string,
  db: D1Database,
  actor: Actor,
  documentData: {
    title: string
    description?: string
    filename: string
    original_filename: string
    file_type: string
    file_size: number
    file_hash?: string
  },
): Promise<any> {
  const properties: DocumentProperties = {
    title: documentData.title,
    description: documentData.description || '',
    filename: documentData.filename,
    original_filename: documentData.original_filename,
    file_type: documentData.file_type,
    file_size: documentData.file_size,
    file_hash: documentData.file_hash,
    published: false,
    download_count: 0,
  }

  return createObject(domain, db, DOCUMENT, properties, actor.id, true)
}



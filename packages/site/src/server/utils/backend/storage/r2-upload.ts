// Multipart upload threshold: 10MB
// Files larger than this will use multipart upload for better performance
const MULTIPART_THRESHOLD = 10 * 1024 * 1024

// Part size for multipart uploads: 5MB (minimum allowed by R2)
const PART_SIZE = 5 * 1024 * 1024

/**
 * Upload file to R2 using multipart upload for better performance and reliability
 *
 * @param bucket - R2 bucket instance
 * @param filename - Target filename in the bucket
 * @param file - File to upload
 */
export async function uploadToR2(bucket: R2Bucket, filename: string, file: File): Promise<void> {
  const httpMetadata = {
    contentType: file.type,
    contentDisposition: `attachment; filename="${file.name}"`,
  }

  // Use multipart upload for files larger than threshold for better performance
  if (file.size > MULTIPART_THRESHOLD) {
    // Create multipart upload
    const multipartUpload = await bucket.createMultipartUpload(filename, {
      httpMetadata,
    })

    try {
      // Calculate number of parts needed
      const numParts = Math.ceil(file.size / PART_SIZE)
      const uploadedParts: R2UploadedPart[] = []

      // Upload parts in parallel for better performance
      const partPromises: Promise<R2UploadedPart>[] = []

      for (let partNumber = 1; partNumber <= numParts; partNumber++) {
        const start = (partNumber - 1) * PART_SIZE
        const end = Math.min(start + PART_SIZE, file.size)
        const partData = file.slice(start, end)

        const partPromise = multipartUpload.uploadPart(partNumber, partData.stream())
        partPromises.push(partPromise)
      }

      // Wait for all parts to upload
      const parts = await Promise.all(partPromises)
      uploadedParts.push(...parts)

      // Complete the multipart upload
      await multipartUpload.complete(uploadedParts)
    }
    catch (error) {
      // Abort the multipart upload on error
      try {
        await multipartUpload.abort()
      }
      catch (abortError) {
        console.error('Failed to abort multipart upload:', abortError)
      }
      throw error
    }
  }
  else {
    // Use regular upload for smaller files
    await bucket.put(filename, file.stream(), {
      httpMetadata,
    })
  }
}
